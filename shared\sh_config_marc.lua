--ONLY CONFIG NAMED sh_config.lua will work!

Config = {}

Config.Debug = false -- Debug
--SERVER SETTINGS
Config.Framework = "auto-detect" -- Framework | types: auto-detect, qbcore, ESX, standalone

Config.NewESX = true

Config.MLO = "marc" -- Maps | types: vanilla, marc, gabz
Config.Target = "auto-detect" -- Target | types: auto-detect, qb-target, qtarget, ox_target
Config.BossMenu = "auto-detect" -- BossMenu | types: auto-detect, esx_society, qb-management
Config.NotificationType = "ox_lib" -- Notifications | types: ESX, ox_lib, qbcore
Config.Progress = "ox_lib" -- ProgressBar | types: progressBars, ox_lib, qbcore
Config.Clothing = "auto-detect" -- Skin / Clothing | types: auto-detect, esx_skin, qb-clothing, fivem-appearance, ox_appearance
Config.Context = "ox_lib" -- Context | types: ox_lib, qbcore
Config.Input = "ox_lib" -- Input | types: ox_lib, qb-input

--PLAYER SETTINGS
Config.JobName = "bahama" -- Job name for bahama
Config.BossGrade = 5 -- Boss Grade
Config.UsingNewQBbankingSocietyExport = true
Config.NeedDuty = true -- Required duty to make drinks etc.
Config.NeedCleanHands = true -- Required to clean hands to make drinks etc.

Config.Logs = { enabled = true, type = "webhook" } -- use webhook or ox_lib (datadog) Can be changed in server > sv_utils.lua
Config.DropPlayer = true -- Drop (Kick) Player if tries to cheat!
Config.AnticheatBan = false -- Change in server/sv_Utils.lua!!! WIll not work by default you need to add your custom trigger to ban player!

Config.Inventory = "auto-detect" -- auto-detect, ox, quasar, chezza, qb, qb-new

Config.Bahama = {
    PolyZone = {
        coords = vector3(-1393.81, -614.06, 31.4), size = vec3(80.0, 80.0, 80), radius = 40.0, debug = false, RemovePeds = true
    },

    Garage = {
        Ped = {
            { Model = "s_m_y_xmech_01", Coords = vec4(-1418.71, -641.39, 27.67, 204.21),
                Scenario = "WORLD_HUMAN_SMOKING"
            }
        },
        Vehicles = {
            { Model = "nspeedo", Label = "Vapid Speedo", livery = 1 },
        },
        SpawnPoints = {
            { Coords = vector3(-1413.72, -640.0, 27.67), Heading = 211.83, Radius = 3.0 },
            { Coords = vector3(-1409.07, -636.74, 27.67), Heading = 211.83, Radius = 3.0 }
        },
    },

    SpawnObjects = true, -- all objects can be found in cl_Utils.lua

    Bars = {
        FrontBar = {
            coords = vector3(-1392.29, -607.66, 30.56),
            radius = 0.7,
            debug = false,
            camera = {
                enabled = false,
                coords = vector3(-1392.7, -606.0, 30.81),
                rotation = vector3(-11.3, 0.0, -148.57),
            },
        },

        BackBar = {
            coords = vector3(-1388.37, -613.54, 30.47),
            radius = 0.7,
            debug = false,
            camera = {
                enabled = false,
                coords = vector3(-1387.0, -614.69, 31.21),
                rotation = vector3(-12.0, 0.0, 34.22),
            },
        },
    },

    Sinks = {
        Bar = {
            coords = vector3(-1386.29, -608.17, 30.15),
            radius = 0.7,
            debug = false,
            WaterStream = vector3(-1386.26, -608.446, 30.42),
        },
    },

    Duty = {
        Main = { coords = vector3(-1373.24, -613.88, 30.84), radius = 0.7, debug = false },
    },

    CloakRoom = { -- CloakRooms
        CloakRoom1 = { coords = vector3(-1366.83, -612.63, 30.61), radius = 0.7, debug = false },
        CloakRoom2 = { coords = vector3(-1368.88, -613.94, 30.49), radius = 0.7, debug = false },
        CloakRoom3 = { coords = vector3(-1383.51, -595.42, 30.91), radius = 0.7, debug = false },
        CloakRoom4 = { coords = vector3(-1381.1, -591.08, 30.82), radius = 0.7, debug = false },
        CloakRoom5 = { coords = vector3(-1379.4, -592.76, 30.9), radius = 0.7, debug = false },
    },

    Stashes = { -- Stashes
        Main = {
            name = "Bahama_Refregiator",
            label = "Bahama Refregiator",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1391.97, -609.47, 29.82),
            radius = 0.7,
            debug = false,
            job = "bahama"
        },
        Refregiator = {
            name = "Bahama_Refregiator",
            label = "Bahama Refregiator",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1391.0, -608.67, 29.84),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator2 = {
            name = "Bahama_Refregiator2",
            label = "Bahama Refregiator2",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1389.9, -608.07, 29.83),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator3 = {
            name = "Bahama_Refregiator3",
            label = "Bahama Refregiator3",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1387.96, -611.18, 29.8),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator4 = {
            name = "Bahama_Refregiator4",
            label = "Bahama Refregiator4",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1388.81, -612.03, 29.81),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator5 = {
            name = "Bahama_Refregiator5",
            label = "Bahama Refregiator5",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1389.94, -612.49, 29.81),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        BackStorage = {
            name = "Bahama_Refregiator6",
            label = "Bahama Refregiator6",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Storage",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1393.99, -628.44, 30.04),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Table = {
            name = "Bahama_Table",
            label = "Bahama Table",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1378.45, -606.88, 30.3),
            radius = 0.7,
            debug = false
        },
        Table2 = {
            name = "Bahama_Table2",
            label = "Bahama Table 2",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1378.11, -604.23, 30.34),
            radius = 0.7,
            debug = false
        },
        Table3 = {
            name = "Bahama_Table3",
            label = "Bahama Table 3",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1382.16, -603.4, 30.36),
            radius = 0.7,
            debug = false
        },
        Table4 = {
            name = "Bahama_Table4",
            label = "Bahama Table 4",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1382.88, -605.73, 30.31),
            radius = 0.7,
            debug = false
        },
        Table5 = {
            name = "Bahama_Table5",
            label = "Bahama Table 5",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1398.51, -619.13, 30.32),
            radius = 0.7,
            debug = false
        },
        Table6 = {
            name = "Bahama_Table6",
            label = "Bahama Table 6",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1400.59, -618.36, 30.31),
            radius = 0.7,
            debug = false
        },
        Table7 = {
            name = "Bahama_Table7",
            label = "Bahama Table 7",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1399.84, -614.59, 30.31),
            radius = 0.7,
            debug = false
        },
        Table8 = {
            name = "Bahama_Table8",
            label = "Bahama Table 8",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1397.18, -614.58, 30.29),
            radius = 0.7,
            debug = false
        },
        Table9 = {
            name = "Bahama_Table9",
            label = "Bahama Table 9",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1395.24, -600.73, 29.99),
            radius = 0.7,
            debug = false
        },

        Table10 = {
            name = "Bahama_Table10",
            label = "Bahama Table 10",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1401.18, -600.04, 29.95),
            radius = 0.7,
            debug = false
        },

        Table11 = {
            name = "Bahama_Table11",
            label = "Bahama Table 11",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1401.99, -605.01, 29.94),
            radius = 0.7,
            debug = false
        },
        Table12 = {
            name = "Bahama_Table12",
            label = "Bahama Table 12",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1407.4, -608.52, 29.91),
            radius = 0.7,
            debug = false
        },

        Table13 = {
            name = "Bahama_Table13",
            label = "Bahama Table 13",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1409.33, -605.17, 29.92),
            radius = 0.7,
            debug = false
        },
        Table14 = {
            name = "Bahama_Table14",
            label = "Bahama Table 14",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1394.26, -595.34, 29.93),
            radius = 0.7,
            debug = false
        },
    },

    BossMenu = {
        Main = { coords = vector3(-1366.03, -622.46, 30.27), radius = 0.7, debug = false },
    },

    IceMachine = {
        IceMachine = { coords = vector3(-1394.91, -611.42, 30.06), radius = 0.7, debug = false },
        IceMachine2 = { coords = vector3(-1386.81, -606.32, 30.08), radius = 0.7, debug = false },
        IceMachine3 = { coords = vector3(-1385.23, -609.9, 30.09), radius = 0.7, debug = false },
        IceMachine4 = { coords = vector3(-1392.54, -614.24, 30.14), radius = 0.7, debug = false },
    },

    Registers = {
        FrontBar = { coords = vector3(-1385.64, -611.71, 30.47), radius = 0.3, debug = false, amount = 0 },
        FrontBar2 = { coords = vector3(-1390.3, -614.79, 30.46), radius = 0.3, debug = false, amount = 0 },
        BackBar = { coords = vector3(-1394.91, -609.64, 30.47), radius = 0.3, debug = false, amount = 0 },
        BackBar2 = { coords = vector3(-1389.05, -605.46, 30.44), radius = 0.3, debug = false, amount = 0 },
        Recipe1 = { coords = vector3(-1384.3, -590.47, 30.45), radius = 0.3, debug = false, amount = 0 },
        Recipe2 = { coords = vector3(-1385.78, -593.3, 30.44), radius = 0.3, debug = false, amount = 0 },
    },

    DancePlatforms = {
        Platform1 = { coords = vector3(-1378.54, -623.31, 29.12), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform2 = { coords = vector3(-1380.28, -620.46, 29.4), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform3 = { coords = vector3(-1382.73, -617.04, 29.13), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform4 = { coords = vector3(-1385.28, -618.82, 29.13), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform5 = { coords = vector3(-1383.21, -621.39, 29.13), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform6 = { coords = vector3(-1381.05, -624.67, 29.06), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform7 = { coords = vector3(-1383.19, -626.08, 29.06), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform8 = { coords = vector3(-1385.14, -623.23, 29.18), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform9 = { coords = vector3(-1387.59, -619.76, 29.13), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        DJ = { coords = vector3(-1379.01, -628.7, 30.64), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@djs@dixon@", anim = "dixn_dance_cntr_open_dix" },
    },

    Teleports = {
        FrontBar = {
            coords = vector3(-1386.99, -605.53, 30.34),
            radius = 0.7,
            debug = false,
            BehindCoords = { coords = vector3(-1387.32, -606.73, 29.32), heading = 172.38 },
            FrontCoords = { coords = vector3(-1387.41, -604.65, 29.32), heading = 3.64 },
        },
        BackBar = {
            coords = vector3(-1391.52, -615.28, 30.65),
            radius = 0.7,
            debug = false,
            BehindCoords = { coords = vector3(-1391.33, -614.16, 29.32), heading = 21.52 },
            FrontCoords = { coords = vector3(-1391.13, -615.85, 29.32), heading = 188.25 },
        },
    }
}

--Drinks
Config.Drinks = {
    PinaColada = {
        Title = "Piña Colada",
        description = "Requirements: Tall glass, Rhum, Ice, Coco Milk",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "coco_milk", count = 1, remove = true },
        },
        AddItems = {
            { item = "pina_colada", count = 1 },
        }
    },
    Mojito = {
        Title = "Mojito",
        description = "Requirements: Tall glass, Rhum, Lime, Sugar, Juice, Mint, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "mint", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
            { item = "sugar", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
        },
        AddItems = {
            { item = "mojito", count = 1 },
        }
    },
    MaiTai = {
        Title = "Mai Tai",
        description = "Requirements: Tall glass, Rhum, Lime, Juice, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
        },
        AddItems = {
            { item = "mai_tai", count = 1 },
        }
    },
    Caipirinha = {
        Title = "Caipirinha",
        description = "Requirements: Tall glass, Rhum, Lime, Sugar, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
            { item = "sugar", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
        },
        AddItems = {
            { item = "caipirinha", count = 1 },
        }
    },
    SanFrancisco = {
        Title = "San Francisco",
        description = "Requirements: Tall glass, Rhum, Juice, Ice",
        RequiredItems = {
            { item = "rhum", count = 1, remove = true },
            { item = "glass_tall", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
        },
        AddItems = {
            { item = "san_francisco", count = 1 },
        }
    },
    BlueLagoon = {
        Title = "Blue Lagoon",
        description = "Requirements: Tall glass, Vodka, Lemon, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "vodka", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lemon", count = 1, remove = true },
        },
        AddItems = {
            { item = "blue_lagoon", count = 1 },
        }
    }
}

--Food
Config.Food = {
    bar_nuts = {
        Title = "Bowl with Nuts",
        description = "Requirements: Bowl, Nuts",
        RequiredItems = {
            { item = "bar_bowl", count = 1, remove = true },
            { item = "nuts", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_nuts", count = 1 },
        }
    },
    bar_beans = {
        Title = "Bowl with Beans",
        description = "Requirements: Bowl, Beans",
        RequiredItems = {
            { item = "bar_bowl", count = 1, remove = true },
            { item = "beans", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_beans", count = 1 },
        }
    },
}


--Sink
Config.Sink = {
    CleanTallGlass = {
        Title = "Clean Tall Glass",
        description = "Requirements: Dirty Tall Glass",
        prop = `prop_sh_tall_glass`,
        RequiredItems = {
            { item = "glass_tall_dirty", count = 1, remove = true },
        },
        AddItems = {
            { item = "glass_tall", count = 1 },
        }
    },
    CleanBowl = {
        Title = "Clean Bowl",
        description = "Requirements: Dirty Bowl",
        prop = `prop_bar_beans`,
        RequiredItems = {
            { item = "bar_bowl_dirty", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_bowl", count = 1 },
        }
    },
}

--IceMachine
Config.IceMachine = {
    Ice = {
        Title = "Get Ice Cube",
        description = "Get ice cube to drinks!",
        RequiredItems = {
            --{ item = "glass_tall_dirty", count = 1, remove = false },
        },
        AddItems = {
            { item = "ice", count = 1 },
        }
    },
}

--BLIPS
Config.Blips = {
    Bahama = { -- do not use same value twice (will result in overwriting of blip)
        BlipCoords = vec3(-1393.6, -604.45, 29.32), -- Blip coords
        Sprite = 93, -- Blip Icon
        Display = 4, -- keep 4
        Scale = 0.8, -- Size of blip
        Colour = 50, -- colour
        Name = "遇见酒吧" -- Blip name
    },
}

--Job BLIPS
Config.JobBlips = {
    PawnShop = { -- do not use same value twice (will result in overwriting of blip)
        BlipCoords = vec3(-1311.47, -1172.07, 3.9), -- Blip coords
        Sprite = 59, -- Blip Icon
        Display = 4, -- keep 4
        Scale = 0.8, -- Size of blip
        Colour = 50, -- colour
        Name = "遇见酒吧 - Shop" -- Blip name
    },
}


--Shop
Config.Shop = {
    Header = "Bahama Shop",
    Items = {
        { label = 'Coco Milk', item = 'coco_milk', description = "Buy Coco Milk for: $", price = 7, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Dirty Tall Glass', item = 'glass_tall_dirty', description = "Buy Dirty Tall Glass for: $", price = 2,
            MinAmount = 1, MaxAmount = 20 },
        { label = 'Juice', item = 'juice', description = "Buy Juice for: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Lemon', item = 'lemon', description = "Buy Lemon for: $", price = 6, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Lime', item = 'lime', description = "Buy Lime for: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Mint', item = 'mint', description = "Buy Mint for: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'rhum', item = 'Rhum', description = "Buy Rhum for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Sugar', item = 'sugar', description = "Buy Sugar for: $", price = 4, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Vodka', item = 'vodka', description = "Buy Vodka for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Dirty bowl', item = 'bar_bowl_dirty', description = "Buy Dirty Bowl for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Nuts', item = 'nuts', description = "Buy Nuts for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Beans', item = 'beans', description = "Buy Beans for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
    },
    Ped = {
        { model = "mp_m_shopkeep_01", coords = vec4(-1312.43, -1171.91, 3.9, 284.6), scenario = "WORLD_HUMAN_SMOKING" },
    },
}

-- Consumables / Drinking / Eating
Config.Consumables = {
    bar_nuts = { -- Item name
        Log = "He ate nuts",
        Remove = true, -- Remove item
        RemoveItem = "bar_nuts", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "bar_bowl_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Eating...",
        duration = 12500,
        Effect = { status = "hunger", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'mp_player_inteat@burger',
                    clip = 'mp_player_int_eat_burger'
                },
                prop = {
                    model = 'prop_bar_nuts',
                    bone = 18905,
                    pos = vec3(0.13, 0.06, 0.02),
                    rot = vec3(-130.0, -7.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    bar_beans = { -- Item name
        Log = "He ate beans",
        Remove = true, -- Remove item
        RemoveItem = "bar_beans", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "bar_bowl_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Eating...",
        duration = 12500,
        Effect = { status = "hunger", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'mp_player_inteat@burger',
                    clip = 'mp_player_int_eat_burger'
                },
                prop = {
                    model = 'prop_bar_beans',
                    bone = 18905,
                    pos = vec3(0.13, 0.06, 0.02),
                    rot = vec3(-130.0, -7.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    pina_colada = { -- Item name
        Log = "Drank Pina colada",
        Remove = true, -- Remove item
        RemoveItem = "pina_colada", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    mojito = { -- Item name
        Log = "Drank mojito",
        Remove = true, -- Remove item
        RemoveItem = "mojito", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    mai_tai = { -- Item name
        Log = "Drank mai_tai",
        Remove = true, -- Remove item
        RemoveItem = "mai_tai", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    caipirinha = { -- Item name
        Log = "Drank caipirinha",
        Remove = true, -- Remove item
        RemoveItem = "caipirinha", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    san_francisco = { -- Item name
        Log = "Drank san_francisco",
        Remove = true, -- Remove item
        RemoveItem = "san_francisco", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    blue_lagoon = { -- Item name
        Log = "Drank blue_lagoon",
        Remove = true, -- Remove item
        RemoveItem = "blue_lagoon", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
}

-- Chairs
Config.ChairsDebug = false
Config.Chairs = {
    --TABLE 1
    {
        coords = vector3(-1393.87, -593.56, 29.85), offsetZ = -0.10, heading = 167.63, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1393.88, -594.24, 29.32, 167.63)
    },
    {
        coords = vector3(-1392.57, -595.55, 29.83), offsetZ = -0.10, heading = 82.12, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1393.05, -595.5, 29.31, 82.12)
    },
    {
        coords = vector3(-1394.55, -596.73, 29.89), offsetZ = -0.10, heading = 349.05, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.6, -596.22, 29.32, 349.05)
    },
    {
        coords = vector3(-1395.63, -594.98, 29.78), offsetZ = -0.10, heading = 258.46, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1395.28, -595.09, 29.32, 258.46)
    },
    --TABLE 2
    {
        coords = vector3(-1393.64, -601.04, 29.89), offsetZ = -0.10, heading = 75.99, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.19, -601.08, 29.32, 75.99)
    },
    {
        coords = vector3(-1394.99, -599.16, 29.88), offsetZ = -0.10, heading = 169.56, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.93, -599.67, 29.44, 169.56)
    },
    {
        coords = vector3(-1396.95, -600.45, 29.9), offsetZ = -0.10, heading = 259.62, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1396.37, -600.79, 29.32, 259.62)
    },
    {
        coords = vector3(-1395.66, -602.34, 29.89), offsetZ = -0.10, heading = 348.32, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1395.53, -601.8, 29.32, 348.32)
    },
    --TABLE 3
    {
        coords = vector3(-1399.57, -600.37, 29.82), offsetZ = -0.10, heading = 76.45, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1399.94, -599.94, 29.32, 76.45)
    },
    {
        coords = vector3(-1400.88, -598.44, 29.86), offsetZ = -0.10, heading = 166.77, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1400.63, -599.22, 29.32, 166.77)
    },
    {
        coords = vector3(-1402.79, -599.82, 29.84), offsetZ = -0.10, heading = 257.91, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1402.02, -599.4, 29.32, 257.91)
    },
    {
        coords = vector3(-1401.5, -601.68, 29.89), offsetZ = -0.10, heading = 348.07, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1401.39, -601.14, 29.32, 348.07)
    },
    --TABLE 4
    {
        coords = vector3(-1400.29, -605.26, 29.8), offsetZ = -0.10, heading = 77.48, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1400.79, -604.92, 29.32, 77.48)
    },
    {
        coords = vector3(-1401.65, -603.36, 29.82), offsetZ = -0.10, heading = 167.17, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1402.07, -603.9, 29.32, 167.17)
    },
    {
        coords = vector3(-1403.46, -604.7, 29.82), offsetZ = -0.10, heading = 254.25, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1403.07, -605.02, 29.32, 254.25)
    },
    {
        coords = vector3(-1402.36, -606.56, 29.87), offsetZ = -0.10, heading = 346.5, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1402.53, -605.87, 29.32, 346.5)
    },
    --Couch
    {
        coords = vector3(-1410.31, -602.44, 29.72), offsetZ = -0.10, heading = 262.95, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1409.44, -602.55, 29.32, 262.95)
    },
    {
        coords = vector3(-1410.84, -603.34, 29.7), offsetZ = -0.10, heading = 211.22, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1410.91, -603.94, 29.32, 211.22)
    },
    {
        coords = vector3(-1411.71, -603.84, 29.72), offsetZ = -0.10, heading = 211.22, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1410.91, -603.94, 29.32, 211.22)
    },
    {
        coords = vector3(-1411.95, -605.14, 29.77), offsetZ = -0.10, heading = 303.03, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1411.42, -605.12, 29.32, 303.03)
    },
    {
        coords = vector3(-1411.56, -606.25, 29.8), offsetZ = -0.10, heading = 302.68, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1410.84, -605.86, 29.32, 302.68)
    },
    {
        coords = vector3(-1410.44, -607.39, 29.71), offsetZ = -0.10, heading = 301.38, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1409.46, -608.15, 29.32, 301.38)
    },
    {
        coords = vector3(-1409.85, -608.36, 29.79), offsetZ = -0.10, heading = 301.38, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1408.69, -609.17, 29.32, 301.38)
    },
    {
        coords = vector3(-1409.15, -609.44, 29.79), offsetZ = -0.10, heading = 301.38, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1408.08, -610.14, 29.32, 301.38)
    },
    {
        coords = vector3(-1408.49, -610.47, 29.73), offsetZ = -0.10, heading = 301.38, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1407.38, -610.51, 29.32, 301.38)
    },
    {
        coords = vector3(-1407.09, -610.98, 29.8), offsetZ = -0.10, heading = 33.43, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1407.38, -610.51, 29.32, 33.43)
    },
    {
        coords = vector3(-1406.04, -610.37, 29.72), offsetZ = -0.10, heading = 33.43, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1406.14, -609.53, 29.31, 33.43)
    },
    {
        coords = vector3(-1405.48, -609.81, 29.66), offsetZ = -0.10, heading = 33.43, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1406.14, -609.53, 29.31, 33.43)
    },
    {
        coords = vector3(-1404.37, -609.1, 29.63), offsetZ = -0.10, heading = 33.43, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1404.91, -608.49, 29.32, 33.43)
    },
    {
        coords = vector3(-1403.18, -608.84, 29.73), offsetZ = -0.10, heading = 336.93, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1403.28, -608.14, 29.32, 336.93)
    },
    --Bar stools 1
    {
        coords = vector3(-1396.46, -610.32, 30.1), offsetZ = -0.10, heading = 241.06, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1396.9, -610.18, 29.32, 241.06)
    },
    {
        coords = vector3(-1395.77, -609.09, 30.14), offsetZ = -0.10, heading = 239.61, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1396.25, -608.92, 29.32, 239.61)
    },
    {
        coords = vector3(-1394.99, -608.11, 30.08), offsetZ = -0.10, heading = 233.97, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1395.38, -607.8, 29.32, 233.97)
    },
    {
        coords = vector3(-1394.17, -606.95, 30.08), offsetZ = -0.10, heading = 224.05, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.65, -606.62, 29.32, 224.05)
    },
    {
        coords = vector3(-1392.68, -605.92, 30.08), offsetZ = -0.10, heading = 213.97, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1392.98, -605.47, 29.32, 213.97)
    },
    {
        coords = vector3(-1391.15, -605.03, 30.09), offsetZ = -0.10, heading = 201.14, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.31, -604.64, 29.32, 201.14)
    },
    {
        coords = vector3(-1389.79, -604.58, 30.07), offsetZ = -0.10, heading = 191.57, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1389.98, -604.31, 29.32, 191.57)
    },
    {
        coords = vector3(-1388.36, -604.27, 30.08), offsetZ = -0.10, heading = 188.38, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1388.53, -603.88, 29.32, 188.38)
    },
    {
        coords = vector3(-1387.07, -604.15, 30.08), offsetZ = -0.10, heading = 183.66, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1387.14, -603.72, 29.32, 183.66)
    },
    --Bar stools 2
    {
        coords = vector3(-1391.56, -616.05, 30.07), offsetZ = -0.10, heading = 12.42, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.48, -616.53, 29.32, 229.07)
    },
    {
        coords = vector3(-1390.1, -615.91, 30.09), offsetZ = -0.10, heading = 16.95, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1389.82, -616.37, 29.32, 229.07)
    },
    {
        coords = vector3(-1388.82, -615.36, 30.09), offsetZ = -0.10, heading = 20.21, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1388.69, -615.85, 29.32, 229.07)
    },
    {
        coords = vector3(-1385.7, -613.56, 30.09), offsetZ = -0.10, heading = 48.64, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1385.49, -613.82, 29.32, 229.07)
    },
    {
        coords = vector3(-1384.8, -612.5, 30.08), offsetZ = -0.10, heading = 52.1, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1384.56, -612.81, 29.32, 229.07)
    },
    {
        coords = vector3(-1384.05, -611.49, 30.1), offsetZ = -0.10, heading = 57.06, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1383.71, -611.83, 29.32, 229.07)
    },
    --BossRoom
    {
        coords = vector3(-1365.0, -624.03, 29.75), offsetZ = -0.10, heading = 32.16, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1366.01, -624.2, 29.33, 49.66)
    },

    {
        coords = vector3(-1368.21, -621.49, 29.83), offsetZ = -0.10, heading = 238.97, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1367.56, -621.71, 29.33, 206.89)
    },

    {
        coords = vector3(-1366.03, -620.0, 29.83), offsetZ = -0.10, heading = 187.4, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1366.24, -620.97, 29.33, 206.89)
    },

}
