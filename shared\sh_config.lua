--只有名为 sh_config.lua 的配置文件才能工作！

Config = {}

Config.Debug = false -- 调试模式
--服务器设置
Config.Framework = "ESX" -- 框架 | 类型: auto-detect, qbcore, ESX, standalone

Config.NewESX = true

Config.MLO = "marc" -- 地图 | 类型: vanilla, marc, gabz
Config.Target = "ox_target" -- 目标系统 | 类型: auto-detect, qb-target, qtarget, ox_target
Config.BossMenu = "esx_society" -- 老板菜单 | 类型: auto-detect, esx_society, qb-management
Config.NotificationType = "ox_lib" -- 通知系统 | 类型: ESX, ox_lib, qbcore
Config.Progress = "ox_lib" -- 进度条 | 类型: progressBars, ox_lib, qbcore
Config.Clothing = "fivem-appearance" -- 皮肤/服装 | 类型: auto-detect, esx_skin, qb-clothing, fivem-appearance, ox_appearance
Config.Context = "ox_lib" -- 上下文菜单 | 类型: ox_lib, qbcore
Config.Input = "ox_lib" -- 输入系统 | 类型: ox_lib, qb-input

--玩家设置
Config.JobName = "bahama" -- 巴哈马酒吧工作名称
Config.BossGrade = 5 -- 老板等级
Config.UsingNewQBbankingSocietyExport = true
Config.NeedDuty = true -- 制作饮品等需要上班
Config.NeedCleanHands = true -- 制作饮品等需要清洁双手

Config.Logs = { enabled = true, type = "webhook" } -- 使用 webhook 或 ox_lib (datadog) 可在 server > sv_utils.lua 中更改
Config.DropPlayer = true -- 如果玩家尝试作弊则踢出玩家！
Config.AnticheatBan = false -- 在 server/sv_Utils.lua 中更改！！！默认不工作，你需要添加自定义触发器来封禁玩家！

Config.Inventory = "ox" -- auto-detect, ox, quasar, chezza, qb, qb-new

Config.Bahama = {
    PolyZone = {
        coords = vector3(-1393.81, -614.06, 31.4), size = vec3(80.0, 80.0, 80), radius = 26.0, debug = false, RemovePeds = true
    },

    Garage = {
        Ped = {
            { Model = "s_m_y_xmech_01", Coords = vec4(-1418.71, -641.39, 27.67, 204.21),
                Scenario = "WORLD_HUMAN_SMOKING"
            }
        },
        Vehicles = {
            { Model = "nspeedo", Label = "Vapid Speedo", livery = 1 },
        },
        SpawnPoints = {
            { Coords = vector3(-1413.72, -640.0, 27.67), Heading = 211.83, Radius = 3.0 },
            { Coords = vector3(-1409.07, -636.74, 27.67), Heading = 211.83, Radius = 3.0 }
        },
    },

    SpawnObjects = true, -- all objects can be found in cl_Utils.lua

    Bars = {
        FrontBar = {
            coords = vector3(-1392.28, -606.1, 30.50),
            radius = 0.7,
            debug = false,
            camera = {
                enabled = false,
                coords = vector3(-1393.55, -606.87, 31.31),
                rotation = vector3(-4.29, 0.0, -61.82),
            },
        },
        BackBar = {
            coords = vector3(-1377.83, -628.84, 31.01),
            radius = 0.7,
            debug = false,
            camera = {
                enabled = false,
                coords = vector3(-1378.726, -627.40, 31.71),
                rotation = vector3(0.0, 0.0, -147.80),
            },
        },
    },

    Sinks = {
        FrontBar = {
            coords = vector3(-1385.4, -608.57, 30.24),
            radius = 0.7,
            debug = false,
            WaterStream = vector3(-1385.65, -608.735, 30.50),
        },
        BackBar = {
            coords = vector3(-1374.89, -627.13, 30.81),
            radius = 0.7,
            debug = false,

            WaterStream = vector3(-1374.72, -626.84, 31.0),
        },
    },

    Duty = {
        Main = { coords = vector3(-1384.45, -605.94, 30.5), radius = 0.7, debug = false },
    },

    CloakRoom = { -- CloakRooms
        Main = { coords = vector3(-1370.66, -625.42, 31.03), radius = 0.7, debug = false },
        Front = { coords = vector3(-1389.15, -592.43, 30.57), radius = 0.7, debug = false },
    },

    Stashes = {
        Main = {
            name = "Bahama_Refregiator",
            label = "巴哈马冰箱",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1389.79, -606.46, 29.71),
            radius = 0.7,
            debug = false,
            job = "bahama"
        },
        Refregiator = {
            name = "Bahama_Refregiator",
            label = "巴哈马冰箱",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1377.71, -631.1, 30.3),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator2 = {
            name = "Bahama_Refregiator2",
            label = "巴哈马冰箱2",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1376.64, -630.08, 30.50),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator3 = {
            name = "Bahama_Refregiator3",
            label = "巴哈马冰箱3",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1375.45, -629.4, 30.29),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator4 = {
            name = "Bahama_Refregiator4",
            label = "巴哈马冰箱4",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1374.34, -628.52, 30.29),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Refregiator5 = {
            name = "Bahama_Refregiator5",
            label = "巴哈马冰箱5",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1373.11, -627.94, 30.31),
            radius = 0.5,
            debug = false,
            job = "bahama"

        },

        Refregiator6 = {
            name = "Bahama_Refregiator6",
            label = "巴哈马冰箱6",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1391.16, -604.72, 30.03),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },
        Refregiator7 = {
            name = "Bahama_Refregiator7",
            label = "巴哈马冰箱7",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "冰箱",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1391.29, -602.99, 29.77),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Table = {
            name = "Bahama_Table",
            label = "巴哈马桌子",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1397.73, -611.13, 30.031),
            radius = 0.7,
            debug = false
        },
        Table2 = {
            name = "Bahama_Table2",
            label = "巴哈马桌子 2",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1399.16, -612.03, 30.036),
            radius = 0.7,
            debug = false
        },
        Table3 = {
            name = "Bahama_Table3",
            label = "巴哈马桌子 3",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1401.59, -604.26, 30.039),
            radius = 0.7,
            debug = false
        },
        Table4 = {
            name = "Bahama_Table4",
            label = "巴哈马桌子 4",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1402.34, -602.65, 30.036),
            radius = 0.7,
            debug = false
        },
        Table5 = {
            name = "Bahama_Table5",
            label = "巴哈马桌子 5",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1398.45, -599.62, 30.11),
            radius = 0.7,
            debug = false
        },
        Table6 = {
            name = "Bahama_Table6",
            label = "巴哈马桌子 6",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1393.64, -620.08, 30.50),
            radius = 0.7,
            debug = false
        },
        Table7 = {
            name = "Bahama_Table7",
            label = "巴哈马桌子 7",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1391.06, -624.23, 30.504),
            radius = 0.7,
            debug = false
        },
        Table8 = {
            name = "Bahama_Table8",
            label = "巴哈马桌子 8",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1381.58, -623.32, 30.504),
            radius = 0.7,
            debug = false
        },
        Table9 = {
            name = "Bahama_Table9",
            label = "巴哈马桌子 9",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "桌子",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1377.87, -621.11, 30.504),
            radius = 0.7,
            debug = false
        },
    },

    BossMenu = {
        Main = { coords = vector3(-1386.85, -627.85, 31.0), radius = 0.7, debug = false },
    },

    IceMachine = {
        FrontBar = { coords = vector3(-1389.55, -599.87, 30.13), radius = 0.7, debug = false },
        BackBar = { coords = vector3(-1372.12, -627.26, 30.505), radius = 0.7, debug = false },
        BackBar2 = { coords = vector3(-1378.72, -631.51, 30.56), radius = 0.7, debug = false },
    },

    Registers = {
        FrontBar = { coords = vector3(-1393.49, -602.36, 30.502), radius = 0.7, debug = false, amount = 0 },
        BackBar = { coords = vector3(-1379.44, -629.75, 30.98), radius = 0.9, debug = false, amount = 0 },
        BackBar2 = { coords = vector3(-1376.29, -628.0, 31.03), radius = 0.9, debug = false, amount = 0 },
        BackBar3 = { coords = vector3(-1372.92, -625.48, 30.97), radius = 0.9, debug = false, amount = 0 },
    },

    DancePlatforms = {
        Platform1 = { coords = vector3(-1387.25, -618.32, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform2 = { coords = vector3(-1388.81, -616.56, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform3 = { coords = vector3(-1387.07, -615.51, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform4 = { coords = vector3(-1385.78, -617.6, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform5 = { coords = vector3(-1384.26, -619.79, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform6 = { coords = vector3(-1385.88, -620.93, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform7 = { coords = vector3(-1387.35, -621.92, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform8 = { coords = vector3(-1388.84, -619.66, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform9 = { coords = vector3(-1390.23, -617.3, 30.032), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Upper1 = { coords = vector3(-1383.46, -612.16, 30.76), radius = 0.9, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Upper2 = { coords = vector3(-1380.15, -617.64, 30.76), radius = 0.9, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        DJ = { coords = vector3(-1381.91, -616.69, 31.55), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@djs@dixon@", anim = "dixn_dance_cntr_open_dix" },
    },

    Teleports = {
        FrontBar = {
            coords = vector3(-1390.56, -598.64, 30.50),
            radius = 0.7,
            debug = false,
            BehindCoords = { coords = vector3(-1390.6, -599.82, 29.32), heading = 179.11 },
            FrontCoords = { coords = vector3(-1391.11, -598.05, 29.32), heading = 60.13 },
        },
        BackBar = {
            coords = vector3(-1380.45, -631.89, 30.93),
            radius = 0.7,
            debug = false,
            BehindCoords = { coords = vector3(-1379.65, -632.0, 30.032), heading = 334.74 },
            FrontCoords = { coords = vector3(-1381.06, -632.57, 30.032), heading = 122.59 },
        },
    }
}

--饮品
Config.Drinks = {
    PinaColada = {
        Title = "椰林飘香",
        description = "需要材料：高杯、朗姆酒、冰块、椰奶",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "coco_milk", count = 1, remove = true },
        },
        AddItems = {
            { item = "pina_colada", count = 1 },
        }
    },
    Mojito = {
        Title = "莫吉托",
        description = "需要材料：高杯、朗姆酒、青柠、糖、果汁、薄荷、冰块",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "mint", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
            { item = "sugar", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
        },
        AddItems = {
            { item = "mojito", count = 1 },
        }
    },
    MaiTai = {
        Title = "迈泰",
        description = "需要材料：高杯、朗姆酒、青柠、果汁、冰块",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
        },
        AddItems = {
            { item = "mai_tai", count = 1 },
        }
    },
    Caipirinha = {
        Title = "凯匹林纳",
        description = "需要材料：高杯、朗姆酒、青柠、糖、冰块",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
            { item = "sugar", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
        },
        AddItems = {
            { item = "caipirinha", count = 1 },
        }
    },
    SanFrancisco = {
        Title = "旧金山",
        description = "需要材料：高杯、朗姆酒、果汁、冰块",
        RequiredItems = {
            { item = "rhum", count = 1, remove = true },
            { item = "glass_tall", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
        },
        AddItems = {
            { item = "san_francisco", count = 1 },
        }
    },
    BlueLagoon = {
        Title = "蓝色泻湖",
        description = "需要材料：高杯、伏特加、柠檬、冰块",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "vodka", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lemon", count = 1, remove = true },
        },
        AddItems = {
            { item = "blue_lagoon", count = 1 },
        }
    }
}

--食物
Config.Food = {
    bar_nuts = {
        Title = "坚果碗",
        description = "需要材料：碗、坚果",
        RequiredItems = {
            { item = "bar_bowl", count = 1, remove = true },
            { item = "nuts", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_nuts", count = 1 },
        }
    },
    bar_beans = {
        Title = "豆子碗",
        description = "需要材料：碗、豆子",
        RequiredItems = {
            { item = "bar_bowl", count = 1, remove = true },
            { item = "beans", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_beans", count = 1 },
        }
    },
}


--水槽
Config.Sink = {
    CleanTallGlass = {
        Title = "清洗高杯",
        description = "需要材料：脏的高杯",
        prop = `prop_sh_tall_glass`,
        RequiredItems = {
            { item = "glass_tall_dirty", count = 1, remove = true },
        },
        AddItems = {
            { item = "glass_tall", count = 1 },
        }
    },
    CleanBowl = {
        Title = "清洗碗",
        description = "需要材料：脏碗",
        prop = `prop_bar_beans`,
        RequiredItems = {
            { item = "bar_bowl_dirty", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_bowl", count = 1 },
        }
    },
}

--制冰机
Config.IceMachine = {
    Ice = {
        Title = "获取冰块",
        description = "获取冰块来制作饮品！",
        RequiredItems = {
            --{ item = "glass_tall_dirty", count = 1, remove = false },
        },
        AddItems = {
            { item = "ice", count = 1 },
        }
    },
}

--BLIPS
Config.Blips = {
    Bahama = { -- do not use same value twice (will result in overwriting of blip)
        BlipCoords = vec3(-1393.6, -604.45, 29.32), -- Blip coords
        Sprite = 93, -- Blip Icon
        Display = 4, -- keep 4
        Scale = 0.8, -- Size of blip
        Colour = 50, -- colour
        Name = "遇见酒吧" -- Blip name
    },
}

--Job BLIPS
Config.JobBlips = {
    PawnShop = { -- do not use same value twice (will result in overwriting of blip)
        BlipCoords = vec3(-1311.47, -1172.07, 3.9), -- Blip coords
        Sprite = 59, -- Blip Icon
        Display = 4, -- keep 4
        Scale = 0.8, -- Size of blip
        Colour = 50, -- colour
        Name = "遇见酒吧 - Shop" -- Blip name
    },
}


--商店
Config.Shop = {
    Header = "巴哈马商店",
    Items = {
        { label = '椰奶', item = 'coco_milk', description = "购买椰奶价格: $", price = 7, MinAmount = 1,
            MaxAmount = 20 },
        { label = '脏高杯', item = 'glass_tall_dirty', description = "购买脏高杯价格: $", price = 2,
            MinAmount = 1, MaxAmount = 20 },
        { label = '果汁', item = 'juice', description = "购买果汁价格: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = '柠檬', item = 'lemon', description = "购买柠檬价格: $", price = 6, MinAmount = 1,
            MaxAmount = 20 },
        { label = '青柠', item = 'lime', description = "购买青柠价格: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = '薄荷', item = 'mint', description = "购买薄荷价格: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = '朗姆酒', item = 'Rhum', description = "购买朗姆酒价格: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = '糖', item = 'sugar', description = "购买糖价格: $", price = 4, MinAmount = 1,
            MaxAmount = 20 },
        { label = '伏特加', item = 'vodka', description = "购买伏特加价格: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = '脏碗', item = 'bar_bowl_dirty', description = "购买脏碗价格: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = '坚果', item = 'nuts', description = "购买坚果价格: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = '豆子', item = 'beans', description = "购买豆子价格: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
    },
    Ped = {
        { model = "mp_m_shopkeep_01", coords = vec4(-1312.43, -1171.91, 3.9, 284.6), scenario = "WORLD_HUMAN_SMOKING" },
    },
}

-- 消耗品 / 饮用 / 食用
Config.Consumables = {
    bar_nuts = { -- 物品名称
        Log = "他吃了坚果",
        Remove = true, -- 移除物品
        RemoveItem = "bar_nuts", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "bar_bowl_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在食用...",
        duration = 12500,
        Effect = { status = "hunger", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'mp_player_inteat@burger',
                    clip = 'mp_player_int_eat_burger'
                },
                prop = {
                    model = 'prop_bar_nuts',
                    bone = 18905,
                    pos = vec3(0.13, 0.06, 0.02),
                    rot = vec3(-130.0, -7.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    bar_beans = { -- 物品名称
        Log = "他吃了豆子",
        Remove = true, -- 移除物品
        RemoveItem = "bar_beans", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "bar_bowl_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在食用...",
        duration = 12500,
        Effect = { status = "hunger", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'mp_player_inteat@burger',
                    clip = 'mp_player_int_eat_burger'
                },
                prop = {
                    model = 'prop_bar_beans',
                    bone = 18905,
                    pos = vec3(0.13, 0.06, 0.02),
                    rot = vec3(-130.0, -7.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    pina_colada = { -- 物品名称
        Log = "喝了椰林飘香",
        Remove = true, -- 移除物品
        RemoveItem = "pina_colada", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "glass_tall_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在饮用...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    mojito = { -- 物品名称
        Log = "喝了莫吉托",
        Remove = true, -- 移除物品
        RemoveItem = "mojito", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "glass_tall_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在饮用...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    mai_tai = { -- 物品名称
        Log = "喝了迈泰",
        Remove = true, -- 移除物品
        RemoveItem = "mai_tai", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "glass_tall_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在饮用...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    caipirinha = { -- 物品名称
        Log = "喝了凯匹林纳",
        Remove = true, -- 移除物品
        RemoveItem = "caipirinha", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "glass_tall_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在饮用...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    san_francisco = { -- 物品名称
        Log = "喝了旧金山",
        Remove = true, -- 移除物品
        RemoveItem = "san_francisco", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "glass_tall_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在饮用...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    blue_lagoon = { -- 物品名称
        Log = "喝了蓝色泻湖",
        Remove = true, -- 移除物品
        RemoveItem = "blue_lagoon", -- 移除物品名称
        RemoveItemCount = 1, -- 移除物品数量
        Add = true,
        AddItem = "glass_tall_dirty", -- 添加物品名称
        AddItemCount = 1, -- 添加物品数量
        ProgressBar = "正在饮用...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
}
Config.ChairsDebug = false
Config.Chairs = {
    --BAR STOOLS FROND
    {
        coords = vector3(-1394.41, -601.91, 30.15), offsetZ = -0.19, heading = 276.33, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.55, -601.16, 29.5, 19.63)
    },
    {
        coords = vector3(-1394.4, -603.36, 30.23), offsetZ = -0.19, heading = 276.33, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.63, -602.79, 29.76, 19.63)
    },
    {
        coords = vector3(-1394.15, -604.77, 30.16), offsetZ = -0.19, heading = 276.33, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.65, -604.37, 30.0, 19.63)
    },
    {
        coords = vector3(-1392.07, -608.3, 30.11), offsetZ = -0.19, heading = 276.33, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1392.75, -608.0, 29.77, 19.63)
    },
    {
        coords = vector3(-1393.67, -605.86, 30.24), offsetZ = -0.19, heading = 318.3, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.37, -606.35, 30.24, 19.63)
    },
    {
        coords = vector3(-1393.05, -607.07, 30.19), offsetZ = -0.19, heading = 312.6, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1393.48, -606.58, 30.03, 19.63)
    },
    {
        coords = vector3(-1391.2, -609.37, 30.32), offsetZ = -0.19, heading = 335.16, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.62, -608.91, 30.032, 19.63)
    },
    {
        coords = vector3(-1390.2, -610.08, 30.17), offsetZ = -0.19, heading = 334.08, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1389.61, -610.59, 29.68, 19.63)
    },
    {
        coords = vector3(-1393.04, -599.33, 30.15), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1393.78, -599.58, 30.03, 19.63)
    },
    {
        coords = vector3(-1391.76, -598.49, 30.38), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.18, -598.02, 30.036, 19.63)
    },
    --BACK STOOLS
    {
        coords = vector3(-1374.17, -625.05, 30.76), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1374.95, -625.26, 30.50, 19.63)
    },

    {
        coords = vector3(-1375.45, -625.87, 30.72), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1375.96, -626.34, 30.14, 19.63)
    },

    {
        coords = vector3(-1376.74, -626.72, 30.71), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1377.3, -627.14, 30.35, 19.63)
    },

    {
        coords = vector3(-1378.21, -627.56, 30.74), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1378.89, -628.14, 30.5, 19.63)
    },
    {
        coords = vector3(-1379.45, -628.48, 30.73), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1380.1, -628.92, 30.14, 19.63)
    },
    {
        coords = vector3(-1380.77, -629.21, 30.81), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1381.6, -629.51, 30.58, 19.63)
    },
    {
        coords = vector3(-1381.95, -630.04, 30.8), offsetZ = -0.19, heading = 221.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1382.27, -630.57, 30.23, 19.63)
    },
    {
        coords = vector3(-1382.03, -631.3, 30.79), offsetZ = -0.19, heading = 285.54, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1382.27, -630.57, 30.23, 19.63)
    },
    --PODIUM SITTING
    {
        coords = vector3(-1377.2, -622.57, 30.50), offsetZ = -0.19, heading = 31.8, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1376.54, -622.09, 29.42, 19.63)
    },
    {
        coords = vector3(-1378.6, -622.17, 30.50), offsetZ = -0.19, heading = 309.46, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1378.74, -620.25, 29.33, 19.63)
    },
    {
        coords = vector3(-1379.16, -621.41, 30.50), offsetZ = -0.19, heading = 309.46, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1378.74, -620.25, 29.33, 19.63)
    },
    {
        coords = vector3(-1379.85, -620.55, 30.50), offsetZ = -0.19, heading = 339.51, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1379.56, -619.99, 30.032, 339.51)
    },
    {
        coords = vector3(-1380.95, -621.27, 30.50), offsetZ = -0.19, heading = 64.27, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1382.12, -621.12, 30.16, 19.63)
    },

    {
        coords = vector3(-1380.59, -622.31, 30.50), offsetZ = -0.19, heading = 118.88, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1381.29, -622.36, 30.032, 150.04)
    },
    {
        coords = vector3(-1380.24, -623.22, 30.50), offsetZ = -0.19, heading = 118.88, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1380.41, -623.76, 30.032, 78.12)
    },
    {
        coords = vector3(-1380.29, -624.31, 30.50), offsetZ = -0.19, heading = 31.17, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1380.41, -623.76, 30.032, 78.12)
    },
    {
        coords = vector3(-1381.18, -624.9, 30.50), offsetZ = -0.19, heading = 31.17, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1381.37, -624.44, 30.032, 28.33)
    },
    {
        coords = vector3(-1382.33, -624.58, 30.50), offsetZ = -0.19, heading = 306.05, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1382.03, -624.27, 30.032, 304.35)
    },
    {
        coords = vector3(-1383.03, -623.84, 30.50), offsetZ = -0.19, heading = 306.05, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1382.59, -623.51, 30.032, 307.43)
    },
    {
        coords = vector3(-1383.85, -622.95, 30.50), offsetZ = -0.19, heading = 352.79, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1383.7, -622.34, 30.032, 352.79)
    },
    {
        coords = vector3(-1384.95, -623.68, 30.50), offsetZ = -0.19, heading = 84.19, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1385.39, -623.49, 30.032, 77.83)
    },
    --TABLE 2
    {
        coords = vector3(-1388.02, -625.55, 30.50), offsetZ = -0.19, heading = 252.4, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1387.63, -625.66, 29.42, 19.63)
    },
    {
        coords = vector3(-1388.96, -624.5, 30.50), offsetZ = -0.19, heading = 341.12, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1388.8, -624.0, 30.032, 359.36)
    },
    {
        coords = vector3(-1389.97, -624.93, 30.50), offsetZ = -0.19, heading = 33.13, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1389.77, -624.07, 30.82, 348.22)
    },
    {
        coords = vector3(-1390.85, -625.4, 30.50), offsetZ = -0.19, heading = 32.21, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.5, -625.07, 30.032, 324.31)
    },
    {
        coords = vector3(-1392.33, -625.29, 30.50), offsetZ = -0.19, heading = 301.11, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.8, -624.47, 30.032, 19.63)
    },
    {
        coords = vector3(-1392.8, -624.57, 30.50), offsetZ = -0.19, heading = 301.11, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.8, -624.47, 30.032, 19.63)
    },
    {
        coords = vector3(-1392.33, -623.21, 30.50), offsetZ = -0.19, heading = 209.85, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1392.08, -623.79, 30.032, 209.85)
    },
    {
        coords = vector3(-1391.48, -622.69, 30.50), offsetZ = -0.19, heading = 209.85, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.64, -623.48, 30.032, 209.42)
    },
    {
        coords = vector3(-1390.9, -621.83, 30.50), offsetZ = -0.19, heading = 255.16, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1390.29, -622.07, 30.032, 255.58)
    },
    {
        coords = vector3(-1391.38, -620.85, 30.50), offsetZ = -0.19, heading = 339.41, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1391.09, -620.29, 30.032, 339.41)
    },
    {
        coords = vector3(-1392.45, -621.18, 30.50), offsetZ = -0.19, heading = 28.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1392.6, -620.47, 30.032, 28.82)
    },
    {
        coords = vector3(-1393.24, -621.78, 30.50), offsetZ = -0.19, heading = 33.94, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1393.53, -621.22, 30.032, 33.94)
    },
    {
        coords = vector3(-1394.58, -621.42, 30.50), offsetZ = -0.19, heading = 303.54, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.36, -620.69, 30.032, 311.72)
    },
    {
        coords = vector3(-1395.11, -620.68, 30.50), offsetZ = -0.19, heading = 303.54, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.36, -620.69, 30.032, 311.72)
    },
    {
        coords = vector3(-1394.87, -619.4, 30.50), offsetZ = -0.19, heading = 213.5, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1394.77, -620.14, 30.032, 200.03)
    },
    {
        coords = vector3(-1393.99, -618.74, 30.50), offsetZ = -0.19, heading = 210.23, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1393.29, -619.01, 30.032, 263.31)
    },
    {
        coords = vector3(-1393.34, -617.98, 30.50), offsetZ = -0.19, heading = 252.08, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1392.56, -618.06, 30.032, 277.5)
    },
    {
        coords = vector3(-1393.82, -617.13, 30.50), offsetZ = -0.19, heading = 350.74, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1393.47, -616.39, 30.032, 337.12)
    },
    --FRONT SITTING
    {
        coords = vector3(-1396.5, -615.31, 30.03), offsetZ = -0.19, heading = 42.57, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1396.64, -614.56, 29.32, 15.92)
    },
    {
        coords = vector3(-1398.38, -616.26, 30.03), offsetZ = -0.19, heading = 346.3, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1398.53, -615.81, 29.32, 15.92)
    },
    {
        coords = vector3(-1398.58, -613.86, 30.03), offsetZ = -0.19, heading = 212.4, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1397.88, -613.94, 29.32, 254.7)
    },
    {
        coords = vector3(-1397.56, -612.88, 30.03), offsetZ = -0.19, heading = 0.27, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1397.6, -612.3, 29.32, 335.03)
    },
    {
        coords = vector3(-1396.07, -611.92, 30.039), offsetZ = -0.19, heading = 71.65, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1396.69, -611.64, 29.33, 71.65)
    },
    {
        coords = vector3(-1396.29, -610.08, 30.031), offsetZ = -0.19, heading = 121.1, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1397.03, -610.44, 29.32, 153.24)
    },
    {
        coords = vector3(-1397.58, -609.04, 30.03), offsetZ = -0.19, heading = 162.2, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1397.9, -609.76, 29.36, 162.2)
    },
    {
        coords = vector3(-1399.92, -610.47, 30.037), offsetZ = -0.19, heading = 234.48, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1399.43, -610.74, 29.32, 249.37)
    },
    {
        coords = vector3(-1396.9, -607.65, 30.03), offsetZ = -0.19, heading = 213.54, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1396.61, -608.22, 29.32, 213.54)
    },
    {
        coords = vector3(-1397.4, -598.47, 30.03), offsetZ = -0.19, heading = 170.66, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1397.43, -599.31, 29.32, 142.85)
    },
    {
        coords = vector3(-1397.36, -600.5, 30.037), offsetZ = -0.19, heading = 38.88, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1398.27, -600.47, 29.32, 38.88)
    },
    {
        coords = vector3(-1399.74, -600.4, 30.031), offsetZ = -0.19, heading = 294.72, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1398.42, -600.69, 29.32, 16.14)
    },
    {
        coords = vector3(-1398.7, -600.92, 29.78), offsetZ = -0.19, heading = 345.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1398.88, -600.65, 29.32, 336.09)
    },
    {
        coords = vector3(-1401.32, -601.6, 30.034), offsetZ = -0.19, heading = 159.72, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1401.64, -602.02, 29.32, 175.58)
    },
    {
        coords = vector3(-1400.08, -602.63, 30.03), offsetZ = -0.19, heading = 132.75, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1400.66, -602.82, 29.32, 155.59)
    },
    {
        coords = vector3(-1400.0, -604.16, 30.03), offsetZ = -0.19, heading = 85.27, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1400.56, -603.73, 29.32, 110.6)
    },
    {
        coords = vector3(-1401.18, -605.86, 30.03), offsetZ = -0.19, heading = 9.72, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1401.4, -605.29, 29.32, 4.67)
    },
    {
        coords = vector3(-1403.13, -604.39, 30.03), offsetZ = -0.19, heading = 303.89, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1403.07, -603.55, 29.32, 314.65)
    },
    {
        coords = vector3(-1404.01, -603.21, 30.03), offsetZ = -0.19, heading = 303.89, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1403.51, -602.72, 29.32, 309.78)
    },
    {
        coords = vector3(-1398.05, -605.14, 30.03), offsetZ = -0.19, heading = 76.76, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1398.79, -605.24, 29.32, 76.78)
    },
    {
        coords = vector3(-1401.5, -606.99, 30.03), offsetZ = -0.19, heading = 77.87, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1401.92, -606.93, 29.31, 76.78)
    },
    {
        coords = vector3(-1402.45, -605.81, 30.03), offsetZ = -0.19, heading = 165.99, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1402.45, -605.81, 30.03, 321.84)
    },
    {
        coords = vector3(-1403.7, -608.37, 30.03), offsetZ = -0.19, heading = 303.67, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1402.78, -607.79, 29.32, 321.84)
    },
    {
        coords = vector3(-1404.13, -607.57, 30.03), offsetZ = -0.19, heading = 303.67, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1403.66, -607.3, 29.32, 321.84)
    },
    {
        coords = vector3(-1404.51, -606.79, 30.03), offsetZ = -0.19, heading = 303.67, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1404.21, -606.55, 29.32, 321.84)
    },
    {
        coords = vector3(-1405.24, -605.96, 30.03), offsetZ = -0.19, heading = 303.67, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1404.74, -605.78, 29.32, 321.84)
    },
    {
        coords = vector3(-1405.79, -604.1, 30.03), offsetZ = -0.19, heading = 252.72, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1405.26, -604.21, 29.31, 251.83)
    },
    {
        coords = vector3(-1391.77, -612.51, 30.03), offsetZ = -0.13, heading = 30.82, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1392.18, -611.97, 29.32, 55.81)

    },

}
