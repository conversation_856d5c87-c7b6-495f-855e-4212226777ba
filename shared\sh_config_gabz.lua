--ONLY CONFIG NAMED sh_config.lua will work!

Config = {}

Config.Debug = false -- Debug
--SERVER SETTINGS
Config.Framework = "auto-detect" -- Framework | types: auto-detect, qbcore, ESX, standalone

Config.NewESX = true

Config.MLO = "gabz" -- Maps | types: vanilla, marc, gabz
Config.Target = "auto-detect" -- Target | types: auto-detect, qb-target, qtarget, ox_target
Config.BossMenu = "auto-detect" -- BossMenu | types: auto-detect, esx_society, qb-management
Config.NotificationType = "ox_lib" -- Notifications | types: ESX, ox_lib, qbcore
Config.Progress = "ox_lib" -- ProgressBar | types: progressBars, ox_lib, qbcore
Config.Clothing = "auto-detect" -- Skin / Clothing | types: auto-detect, esx_skin, qb-clothing, fivem-appearance, ox_appearance
Config.Context = "ox_lib" -- Context | types: ox_lib, qbcore
Config.Input = "ox_lib" -- Input | types: ox_lib, qb-input

--PLAYER SETTINGS
Config.JobName = "bahama" -- Job name for bahama
Config.BossGrade = 5 -- Boss Grade
Config.UsingNewQBbankingSocietyExport = true
Config.NeedDuty = true -- Required duty to make drinks etc.
Config.NeedCleanHands = true -- Required to clean hands to make drinks etc.

Config.Logs = { enabled = true, type = "webhook" } -- use webhook or ox_lib (datadog) Can be changed in server > sv_utils.lua
Config.DropPlayer = true -- Drop (Kick) Player if tries to cheat!
Config.AnticheatBan = false -- Change in server/sv_Utils.lua!!! WIll not work by default you need to add your custom trigger to ban player!

Config.Inventory = "auto-detect" -- auto-detect, ox, quasar, chezza, qb, qb-new

Config.Bahama = {
    PolyZone = {
        coords = vector3(-1393.81, -614.06, 31.4), size = vec3(80.0, 80.0, 80), radius = 26.0, debug = false, RemovePeds = true
    },

    Garage = {
        Ped = {
            { Model = "s_m_y_xmech_01", Coords = vec4(-1451.9303, -579.5816, 30.2488, 307.2315),
                Scenario = "WORLD_HUMAN_SMOKING"
            }
        },
        Vehicles = {
            { Model = "nspeedo", Label = "Vapid Speedo", livery = 1 },
        },
        SpawnPoints = {
            { Coords = vector3(-1453.3247, -594.2168, 30.7968), Heading = 302.8214, Radius = 3.0 },
            { Coords = vector3(-1444.2007, -588.1982, 30.7169), Heading = 300.0309, Radius = 3.0 }
        },
    },

    SpawnObjects = true, -- all objects can be found in cl_Utils.lua

    Bars = {
        FrontBar = {
            coords = vector3(-1399.9056, -598.1368, 30.5810),
            radius = 0.7,
            debug = false,
            camera = {
                enabled = false,
                coords = vector3(-1401.0039, -597.6840, 31.3200),
                rotation = vector3(-10.29, 0.0, -90.82),
            },
        },
    },

    Sinks = {
        FrontBar = {
            coords = vector3(-1403.0237, -597.9800, 30.7109),
            radius = 0.7,
            debug = false,
            WaterStream = vector3(-1403.0237, -597.9800, 30.7109),
        },
    },

    Duty = {
        Main = { coords = vector3(-1377.5459, -629.5012, 30.9456), radius = 0.7, debug = false },
    },

    CloakRoom = { -- CloakRooms
        Main = { coords = vector3(-1383.7253, -637.9737, 30.6644), radius = 0.7, debug = false },
        Front = { coords = vector3(-1381.2996, -636.2637, 30.5538), radius = 0.7, debug = false },
    },

    Stashes = {
        Main = {
            name = "Bahama_Refregiator",
            label = "Bahama Refregiator",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1401.8584, -597.3342, 30.2305),
            radius = 0.7,
            debug = false,
            job = "bahama"
        },
        Refregiator = {
            name = "Bahama_Refregiator",
            label = "Bahama Refregiator",
            TargetIcon = "fas fa-ice-cream",
            TargetLabel = "Refregiator",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1404.0139, -598.9229, 29.9120),
            radius = 0.5,
            debug = false,
            job = "bahama"
        },

        Table = {
            name = "Bahama_Table",
            label = "Bahama Table",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1406.2240, -601.8562, 29.7466),
            radius = 0.9,
            debug = false
        },
        Table2 = {
            name = "Bahama_Table2",
            label = "Bahama Table 2",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1408.1309, -603.1403, 29.7890),
            radius = 0.9,
            debug = false
        },
        Table3 = {
            name = "Bahama_Table3",
            label = "Bahama Table 3",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1410.2789, -604.5897, 29.7636),
            radius = 0.9,
            debug = false
        },
        Table4 = {
            name = "Bahama_Table4",
            label = "Bahama Table 4",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1397.5894, -596.0779, 29.7806),
            radius = 0.9,
            debug = false
        },
        Table5 = {
            name = "Bahama_Table5",
            label = "Bahama Table 5",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1395.7341, -594.8348, 29.7259),
            radius = 0.9,
            debug = false
        },
        Table6 = {
            name = "Bahama_Table6",
            label = "Bahama Table 6",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1393.9176, -593.5966, 29.7722),
            radius = 0.9,
            debug = false
        },
        Table7 = {
            name = "Bahama_Table7",
            label = "Bahama Table 7",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1387.5380, -606.9895, 29.7922),
            radius = 0.9,
            debug = false
        },
        Table8 = {
            name = "Bahama_Table8",
            label = "Bahama Table 8",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1386.6042, -608.5151, 29.7737),
            radius = 0.9,
            debug = false
        },
        Table9 = {
            name = "Bahama_Table9",
            label = "Bahama Table 9",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1381.6158, -616.3422, 29.7156),
            radius = 0.9,
            debug = false
        },
        Table10 = {
            name = "Bahama_Table9",
            label = "Bahama Table 9",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1380.7223, -617.8027, 29.7594),
            radius = 0.9,
            debug = false
        },
        Table11 = {
            name = "Bahama_Table9",
            label = "Bahama Table 9",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1381.3707, -627.3315, 29.7649),
            radius = 0.9,
            debug = false
        },
        Table12 = {
            name = "Bahama_Table9",
            label = "Bahama Table 9",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1383.4579, -628.7543, 29.7835),
            radius = 0.9,
            debug = false
        },
        Table13 = {
            name = "Bahama_Table9",
            label = "Bahama Table 9",
            TargetIcon = "fas fa-tablet",
            TargetLabel = "Table",
            Slots = 20,
            Weight = 50000, -- 50 KG
            coords = vector3(-1385.6725, -630.1819, 29.7323),
            radius = 0.9,
            debug = false
        },
    },

    BossMenu = {
        Main = { coords = vector3(-1370.2504, -625.8503, 30.6295), radius = 0.7, debug = false },
    },

    IceMachine = {
        FrontBar = { coords = vector3(-1403.0432, -597.9908, 30.3546), radius = 0.7, debug = false },
    },

    Registers = {
        FrontBar = { coords = vector3(-1398.8019, -600.0894, 30.2683), radius = 0.7, debug = false, amount = 0 },
        BackBar = { coords = vector3(-1402.8275, -602.5941, 30.0537), radius = 0.9, debug = false, amount = 0 },
    },

    DancePlatforms = {
        Platform1 = { coords = vector3(-1381.6162, -608.8322, 30.1149), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform2 = { coords = vector3(-1379.9003, -611.0944, 30.1199), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform3 = { coords = vector3(-1378.6956, -609.7618, 30.1199), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform4 = { coords = vector3(-1393.1335, -612.3130, 30.2028), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform5 = { coords = vector3(-1390.9276, -617.2913, 30.2029), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform6 = { coords = vector3(-1388.0093, -620.8530, 30.2030), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        Platform7 = { coords = vector3(-1391.1606, -631.9621, 31.3041), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@mini@dance@dance_solo@female@var_b@", anim = "high_center" },
        DJ = { coords = vector3(-1376.7792, -607.6235, 31.3199), radius = 0.7, debug = false,
            dict = "anim@amb@nightclub@djs@dixon@", anim = "dixn_dance_cntr_open_dix" },
    },

    Teleports = {
        FrontBar = {
            coords = vector3(0, 0, 0),
            radius = 0.7,
            debug = false,
            BehindCoords = { coords = vector3(-0, 0, 0), heading = 0 },
            FrontCoords = { coords = vector3(0, 0, 0), heading = 0 },
        },
        BackBar = {
            coords = vector3(0, 0, 0),
            radius = 0.7,
            debug = false,
            BehindCoords = { coords = vector3(0, 0, 0), heading = 0 },
            FrontCoords = { coords = vector3(0, 0, 0), heading = 0 },
        },
    }
}
--Drinks
Config.Drinks = {
    PinaColada = {
        Title = "Piña Colada",
        description = "Requirements: Tall glass, Rhum, Ice, Coco Milk",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "coco_milk", count = 1, remove = true },
        },
        AddItems = {
            { item = "pina_colada", count = 1 },
        }
    },
    Mojito = {
        Title = "Mojito",
        description = "Requirements: Tall glass, Rhum, Lime, Sugar, Juice, Mint, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "mint", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
            { item = "sugar", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
        },
        AddItems = {
            { item = "mojito", count = 1 },
        }
    },
    MaiTai = {
        Title = "Mai Tai",
        description = "Requirements: Tall glass, Rhum, Lime, Juice, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
        },
        AddItems = {
            { item = "mai_tai", count = 1 },
        }
    },
    Caipirinha = {
        Title = "Caipirinha",
        description = "Requirements: Tall glass, Rhum, Lime, Sugar, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "rhum", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lime", count = 1, remove = true },
            { item = "sugar", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
        },
        AddItems = {
            { item = "caipirinha", count = 1 },
        }
    },
    SanFrancisco = {
        Title = "San Francisco",
        description = "Requirements: Tall glass, Rhum, Juice, Ice",
        RequiredItems = {
            { item = "rhum", count = 1, remove = true },
            { item = "glass_tall", count = 1, remove = true },
            { item = "juice", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
        },
        AddItems = {
            { item = "san_francisco", count = 1 },
        }
    },
    BlueLagoon = {
        Title = "Blue Lagoon",
        description = "Requirements: Tall glass, Vodka, Lemon, Ice",
        RequiredItems = {
            { item = "glass_tall", count = 1, remove = true },
            { item = "vodka", count = 1, remove = true },
            { item = "ice", count = 1, remove = true },
            { item = "lemon", count = 1, remove = true },
        },
        AddItems = {
            { item = "blue_lagoon", count = 1 },
        }
    }
}

--Food
Config.Food = {
    bar_nuts = {
        Title = "Bowl with Nuts",
        description = "Requirements: Bowl, Nuts",
        RequiredItems = {
            { item = "bar_bowl", count = 1, remove = true },
            { item = "nuts", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_nuts", count = 1 },
        }
    },
    bar_beans = {
        Title = "Bowl with Beans",
        description = "Requirements: Bowl, Beans",
        RequiredItems = {
            { item = "bar_bowl", count = 1, remove = true },
            { item = "beans", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_beans", count = 1 },
        }
    },
}


--Sink
Config.Sink = {
    CleanTallGlass = {
        Title = "Clean Tall Glass",
        description = "Requirements: Dirty Tall Glass",
        prop = `prop_sh_tall_glass`,
        RequiredItems = {
            { item = "glass_tall_dirty", count = 1, remove = true },
        },
        AddItems = {
            { item = "glass_tall", count = 1 },
        }
    },
    CleanBowl = {
        Title = "Clean Bowl",
        description = "Requirements: Dirty Bowl",
        prop = `prop_bar_beans`,
        RequiredItems = {
            { item = "bar_bowl_dirty", count = 1, remove = true },
        },
        AddItems = {
            { item = "bar_bowl", count = 1 },
        }
    },
}

--IceMachine
Config.IceMachine = {
    Ice = {
        Title = "Get Ice Cube",
        description = "Get ice cube to drinks!",
        RequiredItems = {
            --{ item = "glass_tall_dirty", count = 1, remove = false },
        },
        AddItems = {
            { item = "ice", count = 1 },
        }
    },
}

--BLIPS
Config.Blips = {
    Bahama = { -- do not use same value twice (will result in overwriting of blip)
        BlipCoords = vec3(-1393.6, -604.45, 29.32), -- Blip coords
        Sprite = 93, -- Blip Icon
        Display = 4, -- keep 4
        Scale = 0.8, -- Size of blip
        Colour = 50, -- colour
        Name = "遇见酒吧" -- Blip name
    },
}

--Job BLIPS
Config.JobBlips = {
    PawnShop = { -- do not use same value twice (will result in overwriting of blip)
        BlipCoords = vec3(-1311.47, -1172.07, 3.9), -- Blip coords
        Sprite = 59, -- Blip Icon
        Display = 4, -- keep 4
        Scale = 0.8, -- Size of blip
        Colour = 50, -- colour
        Name = "遇见酒吧 - Shop" -- Blip name
    },
}



--Shop
Config.Shop = {
    Header = "Bahama Shop",
    Items = {
        { label = 'Coco Milk', item = 'coco_milk', description = "Buy Coco Milk for: $", price = 7, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Dirty Tall Glass', item = 'glass_tall_dirty', description = "Buy Dirty Tall Glass for: $", price = 2,
            MinAmount = 1, MaxAmount = 20 },
        { label = 'Juice', item = 'juice', description = "Buy Juice for: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Lemon', item = 'lemon', description = "Buy Lemon for: $", price = 6, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Lime', item = 'lime', description = "Buy Lime for: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Mint', item = 'mint', description = "Buy Mint for: $", price = 5, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'rhum', item = 'Rhum', description = "Buy Rhum for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Sugar', item = 'sugar', description = "Buy Sugar for: $", price = 4, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Vodka', item = 'vodka', description = "Buy Vodka for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Dirty bowl', item = 'bar_bowl_dirty', description = "Buy Dirty Bowl for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Nuts', item = 'nuts', description = "Buy Nuts for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
        { label = 'Beans', item = 'beans', description = "Buy Beans for: $", price = 8, MinAmount = 1,
            MaxAmount = 20 },
    },
    Ped = {
        { model = "mp_m_shopkeep_01", coords = vec4(-1312.43, -1171.91, 3.9, 284.6), scenario = "WORLD_HUMAN_SMOKING" },
    },
}

-- Consumables / Drinking / Eating
Config.Consumables = {
    bar_nuts = { -- Item name
        Log = "He ate nuts",
        Remove = true, -- Remove item
        RemoveItem = "bar_nuts", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "bar_bowl_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Eating...",
        duration = 12500,
        Effect = { status = "hunger", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'mp_player_inteat@burger',
                    clip = 'mp_player_int_eat_burger'
                },
                prop = {
                    model = 'prop_bar_nuts',
                    bone = 18905,
                    pos = vec3(0.13, 0.06, 0.02),
                    rot = vec3(-130.0, -7.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    bar_beans = { -- Item name
        Log = "He ate beans",
        Remove = true, -- Remove item
        RemoveItem = "bar_beans", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "bar_bowl_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Eating...",
        duration = 12500,
        Effect = { status = "hunger", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'mp_player_inteat@burger',
                    clip = 'mp_player_int_eat_burger'
                },
                prop = {
                    model = 'prop_bar_beans',
                    bone = 18905,
                    pos = vec3(0.13, 0.06, 0.02),
                    rot = vec3(-130.0, -7.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    pina_colada = { -- Item name
        Log = "Drank Pina colada",
        Remove = true, -- Remove item
        RemoveItem = "pina_colada", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    mojito = { -- Item name
        Log = "Drank mojito",
        Remove = true, -- Remove item
        RemoveItem = "mojito", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    mai_tai = { -- Item name
        Log = "Drank mai_tai",
        Remove = true, -- Remove item
        RemoveItem = "mai_tai", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    caipirinha = { -- Item name
        Log = "Drank caipirinha",
        Remove = true, -- Remove item
        RemoveItem = "caipirinha", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },

            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    san_francisco = { -- Item name
        Log = "Drank san_francisco",
        Remove = true, -- Remove item
        RemoveItem = "san_francisco", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
    blue_lagoon = { -- Item name
        Log = "Drank blue_lagoon",
        Remove = true, -- Remove item
        RemoveItem = "blue_lagoon", -- Remove Item name
        RemoveItemCount = 1, -- Remove Item Count
        Add = true,
        AddItem = "glass_tall_dirty", -- Remove Item name
        AddItemCount = 1, -- Remove Item Count
        ProgressBar = "Drinking...",
        duration = 12500,
        Effect = { status = "drunk", add = 100000 },
        animation = {
            emote = {
                enabled = true,
                anim = {
                    dict = 'amb@world_human_drinking@coffee@male@idle_a',
                    clip = 'idle_c'
                },
                prop = {
                    model = 'prop_cocktail',
                    bone = 57005,
                    pos = vec3(0.14, -0.07, -0.01),
                    rot = vec3(-80.0, 100.0, 0.0)
                },
            },
            scenario = {
                enabled = false,
                anim = {
                    scenario = "WORLD_HUMAN_SMOKING_POT"
                },
            },
        }
    },
}
Config.ChairsDebug = false
Config.Chairs = {
    --BAR STOOLS FROND
    {
        coords = vector3(-1397.9855, -598.7284, 30.2367), offsetZ = -0.19, heading = 124.73, radius = 0.5, distance = 1.7,  
        LeaveCoords = vector4(-1398.2654, -598.0298, 29.3200, 136.6345)
    },
    {
        coords = vector3(-1397.4899, -600.7247, 30.2367), offsetZ = -0.19, heading = 95.4225, radius = 0.5, distance = 1.7,
        LeaveCoords = vector4(-1396.6988, -600.1550, 29.3199, 94.4844)
    },
    {
        coords = vector3(-1398.2827, -603.0336, 30.2367), offsetZ = -0.19, heading = 48.8819, radius = 0.5, distance = 1.7,   
        LeaveCoords = vector4(-1397.4253, -602.6644, 29.3199, 61.1017)
    },
    {
        coords = vector3(-1400.2518, -604.3049, 30.2367), offsetZ = -0.19, heading = 23.5592, radius = 0.5, distance = 1.7,   
        LeaveCoords = vector4(-1399.3850, -604.3951, 29.3199, 33.3959)
    },
    {
        coords = vector3(-1402.6117, -604.2498, 30.2367), offsetZ = -0.19, heading = 345.3981, radius = 0.5, distance = 1.7,   
        LeaveCoords = vector4(-1402.1371, -605.1754, 29.3199, 345.4432)
    },
    {
        coords = vector3(-1404.4955, -602.7792, 30.2367), offsetZ = -0.19, heading = 314.4536, radius = 0.5, distance = 1.7,     
        LeaveCoords = vector4(-1404.3387, -603.7480, 29.3199, 318.3234)
    },
    
    -- NEX TO BAR 
    {
        coords = vector3(-1406.4120, -600.5626, 29.9), offsetZ = -0.19, heading = 209.4663, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1405.6835, -600.7562, 29.3199, 209.7889)
    }, 
    {
        coords = vector3(-1407.2502, -601.0855, 29.9), offsetZ = -0.19, heading = 216.2151, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1407.4270, -601.9100, 29.3200, 216.6869)
    }, 
    {
        coords = vector3(-1408.5128, -601.7563, 29.9), offsetZ = -0.19, heading = 218.1750, radius = 0.5, distance = 1.7,     
        LeaveCoords = vector4(-1407.7012, -602.2567, 29.3199, 214.7115)
    },
    {
        coords = vector3(-1409.4957, -602.4496, 29.9), offsetZ = -0.19, heading = 213.5022, radius = 0.5, distance = 1.7,      
        LeaveCoords = vector4(-1409.2445, -603.5320, 29.3198, 218.8671)
    },
    {
        coords = vector3(-1410.5784, -603.1826, 29.9), offsetZ = -0.19, heading = 210.9522, radius = 0.5, distance = 1.7,       
        LeaveCoords = vector4(-1409.9552, -603.5710, 29.3197, 215.6341)
    },
    {
        coords = vector3(-1411.8556, -605.0869, 29.9), offsetZ = -0.19, heading = 304.7330, radius = 0.5, distance = 1.7,        
        LeaveCoords = vector4(-1410.9489, -605.1885, 29.3199, 214.7063)
    },
    ---privátky
    {
        coords = vector3(-1408.2351, -610.5545, 30.3), offsetZ = -0.19, heading = 303.7378, radius = 0.5, distance = 1.7,       
        LeaveCoords = vector4(-1407.1957, -609.9520, 29.6784, 302.1729)
    },
    {
        coords = vector3(-1406.4984, -613.2307, 30.3), offsetZ = -0.19, heading = 304.6137, radius = 0.5, distance = 1.7,        
        LeaveCoords = vector4(-1405.8359, -612.8199, 29.6780, 302.7524)
    },
    {
        coords = vector3(-1404.7570, -615.8337, 30.3), offsetZ = -0.19, heading = 300.8400, radius = 0.5, distance = 1.7,       
        LeaveCoords = vector4(-1404.0906, -615.4484, 29.6792, 299.9590)
    },
    {
        coords = vector3(-1403.0267, -618.5771, 30.3), offsetZ = -0.19, heading = 308.5364, radius = 0.5, distance = 1.7,       
        LeaveCoords = vector4(-1402.0371, -617.9179, 29.6782, 303.6781)
    },
    --Židle Mid
    {
        coords = vector3(-1399.6687, -609.6740, 30.33), offsetZ = -0.19, heading = 267.1149, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1399.6339, -608.7968, 29.3199, 246.2461)
    },
    {
        coords = vector3(-1399.5687, -613.4918, 30.33), offsetZ = -0.19, heading = 300.6032, radius = 0.5, distance = 1.7,       
        LeaveCoords = vector4(-1399.8232, -612.6310, 29.3200, 282.7463)
    },
    {
        coords = vector3(-1397.6533, -616.7444, 30.33), offsetZ = -0.19, heading = 313.1064, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1397.9683, -616.0707, 29.3199, 304.7456)
    },
    {
        coords = vector3(-1393.6298, -623.0226, 30.33), offsetZ = -0.19, heading = 304.7268, radius = 0.5, distance = 1.7,       
        LeaveCoords = vector4(-1393.9784, -622.4432, 29.3199, 303.6657)
    },
    {
        coords = vector3(-1391.5103, -626.0700, 30.33), offsetZ = -0.19, heading = 320.5804, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1391.7944, -625.6502, 29.3200, 308.1353)
    },
    {
        coords = vector3(-1388.0349, -627.8823, 30.33), offsetZ = -0.19, heading = 357.0142, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1388.5920, -627.7104, 29.3198, 348.5792) 
    },
    -- SEDAČKY RIGHT SIDE
    {
        coords = vector3(-1400.5950, -619.5111, 29.9), offsetZ = -0.19, heading = 219.5443, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1399.4053, -619.6085, 29.3199, 213.2686) 
    },
    {
        coords = vector3(-1401.1659, -621.5125, 29.9), offsetZ = -0.19, heading = 308.6077, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1400.2389, -621.6190, 29.3198, 301.5546) 
    },
    {
        coords = vector3(-1400.3390, -622.8234, 29.9), offsetZ = -0.19, heading = 299.0694, radius = 0.5, distance = 1.7,       
        LeaveCoords = vector4(-1400.0475, -622.0729, 29.3169, 310.6060) 
    },
    {
        coords = vector3(-1399.1150, -624.7975, 29.9), offsetZ = -0.19, heading = 307.6593, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1399.0063, -623.5135, 29.3199, 310.7884) 
    },
    {
        coords = vector3(-1398.2574, -625.9656, 29.9), offsetZ = -0.19, heading = 303.2836, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1396.9874, -626.2210, 29.3200, 296.0160)  
    },
    {
        coords = vector3(-1397.1915, -627.6281, 29.9), offsetZ = -0.19, heading = 306.2831, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1396.6779, -626.7568, 29.3198, 309.0459) 
    },
    {
    coords = vector3(-1396.0968, -629.3370, 29.9), offsetZ = -0.19, heading = 320.0971, radius = 0.5, distance = 1.7,         
    LeaveCoords = vector4(-1395.0487, -629.0485, 29.3199, 300.0377) 
    },
    --SEDAČKY VZADU
    {
        coords = vector3(-1385.3109, -631.5105, 29.9), offsetZ = -0.19, heading = 35.3248, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1386.2429, -630.9659, 29.3198, 34.9878) 
    },
    {
        coords = vector3(-1384.4243, -630.9716, 29.9), offsetZ = -0.19, heading = 33.8893, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1384.7775, -629.9412, 29.3199, 32.1588) 
    },
    {
        coords = vector3(-1383.2209, -630.0599, 29.9), offsetZ = -0.19, heading = 31.6104, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1384.0815, -629.4641, 29.3199, 36.7734) 
    },
    {
        coords = vector3(-1382.4230, -629.5674, 29.9), offsetZ = -0.19, heading = 36.6778, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1382.5692, -628.5335, 29.3199, 30.8984) 
    },
    {
        coords = vector3(-1381.1677, -628.7653, 29.9), offsetZ = -0.19, heading = 31.1519, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1381.4330, -628.4642, 29.3198, 36.2305) 
    },
    {
        coords = vector3(-1380.4172, -628.2487, 29.9), offsetZ = -0.19, heading = 28.1539, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1380.4027, -627.1102, 29.3197, 33.5879) 
    },
    --OFFICE
    {
        coords = vector3(-1373.3407, -632.1631, 29.9), offsetZ = -0.19, heading = 39.0912, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1373.5837, -631.7558, 29.3187, 35.7684) 
    },
    {
        coords = vector3(-1374.0771, -632.6763, 29.9), offsetZ = -0.19, heading = 28.4397, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1374.3203, -632.2675, 29.3135, 30.5196) 
    },
    {
        coords = vector3(-1373.5022, -630.2028, 29.9), offsetZ = -0.19, heading = 127.3153, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1373.9912, -630.5214, 29.3043, 125.5836) 
    },
    {
        coords = vector3(-1373.0803, -630.9236, 29.9), offsetZ = -0.19, heading = 122.8226, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1373.5313, -631.2026, 29.3475, 123.2407) 
    },
    {
        coords = vector3(-1375.1478, -625.2562, 29.9), offsetZ = -0.19, heading = 306.8728, radius = 0.5, distance = 1.7,            
        LeaveCoords = vector4(-1374.5120, -624.7886, 29.3199, 306.4039) 
    },
    {
        coords = vector3(-1375.7578, -624.4129, 29.9), offsetZ = -0.19, heading = 302.0122, radius = 0.5, distance = 1.7,            
        LeaveCoords = vector4(-1375.1675, -624.0005, 29.3170, 306.4997) 
    },
    --middle židle 2
    {
        coords = vector3(-1381.9047, -623.7638, 30.33), offsetZ = -0.19, heading = 85.1958, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1381.8777, -622.7471, 29.3199, 91.8321)
    },
    {
        coords = vector3(-1382.1608, -619.9227, 30.33), offsetZ = -0.19, heading = 119.1359, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1382.5936, -619.3513, 29.3199, 114.2237)
    },
    {
        coords = vector3(-1384.1422, -616.7640, 30.33), offsetZ = -0.19, heading = 127.3906, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1384.3953, -616.3329, 29.3199, 119.5910)
    },
    --LEFT SIDE SEDAČKY
    {
        coords = vector3(-1379.3451, -618.6907, 29.99), offsetZ = -0.19, heading = 48.2519, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1379.6134, -618.3892, 29.3188, 40.3849)
    },
    {
        coords = vector3(-1379.4171, -617.2409, 29.99), offsetZ = -0.19, heading = 122.3370, radius = 0.5, distance = 1.7,         
        LeaveCoords = vector4(-1379.8171, -617.4958, 29.2987, 121.8498)
    },
    {
        coords = vector3(-1380.5873, -615.2937, 29.99), offsetZ = -0.19, heading = 121.8538, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1381.0587, -615.6091, 29.3135, 125.4799)
    },
    {
        coords = vector3(-1382.0105, -614.7851, 29.99), offsetZ = -0.19, heading = 214.4997, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1382.0297, -614.7103, 29.7641, 214.4997)
    }, 
    --RIGHT SIDE ŽIDLE 2
    {
        coords = vector3(-1388.1819, -610.4626, 30.33), offsetZ = -0.19, heading = 127.5148, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1388.6429, -610.1366, 29.3199, 125.7720)
    }, 
    {
        coords = vector3(-1390.3345, -607.3530, 30.33), offsetZ = -0.19, heading = 141.4545, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1390.9264, -607.1271, 29.3198, 149.9603)
    },
    {
        coords = vector3(-1393.8810, -605.7758, 30.33), offsetZ = -0.19, heading = 180.6764, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1394.5553, -606.0449, 29.3198, 185.4489)
    },
    --LEFT SIDE SEDAČKY 2
    {
        coords = vector3(-1385.3566, -609.5938, 29.99), offsetZ = -0.19, heading = 38.6882, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1385.6733, -609.2034, 29.3191, 29.9336)
    },
    {
        coords = vector3(-1385.3519, -608.0377, 29.99), offsetZ = -0.19, heading = 124.1269, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1385.8315, -608.3320, 29.3197, 131.2146)
    },
    {
        coords = vector3(-1386.7238, -606.0457, 29.99), offsetZ = -0.19, heading = 122.8695, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1387.1384, -606.3171, 29.3136, 123.7359)
    },
    {
        coords = vector3(-1388.0712, -605.5246, 29.99), offsetZ = -0.19, heading = 215.1715, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1387.7904, -605.9664, 29.3193, 216.4981)
    },
    --CENTER MID ŽIDLE
    {
        coords = vector3(-1393.9409, -610.2501, 29.7), offsetZ = -0.19, heading = 192.1000, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1393.9568, -609.7552, 28.7199, 28.0341)
    },
    {
        coords = vector3(-1392.8724, -610.5693, 29.7), offsetZ = -0.19, heading = 161.0063, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1392.6458, -610.0379, 28.7199, 346.8489)
    },
    {
        coords = vector3(-1391.9576, -613.1290, 29.7), offsetZ = -0.19, heading = 70.1007, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1391.5435, -613.2717, 28.7180, 249.3758)
    },
    {
        coords = vector3(-1393.4442, -614.3910, 29.7), offsetZ = -0.19, heading = 16.4962, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1393.4855, -614.8269, 28.7199, 190.2804)
    },
    {
        coords = vector3(-1394.7185, -614.0052, 29.7), offsetZ = -0.19, heading = 340.4744, radius = 0.5, distance = 1.7,           
        LeaveCoords = vector4(-1394.9650, -614.4034, 28.7199, 155.4616)
    },
    {
        coords = vector3(-1395.5574, -611.1800, 29.7), offsetZ = -0.19, heading = 248.3550, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1395.9142, -610.9233, 28.7163, 65.8935)
    },
    {
        coords = vector3(-1391.7460, -611.7570, 29.7), offsetZ = -0.19, heading = 101.7956, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1391.1912, -611.6486, 28.7149, 291.5381)
    }, 
    {
        coords = vector3(-1395.8026, -612.5377, 29.7), offsetZ = -0.19, heading = 281.5109, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1396.3077, -612.4927, 28.7194, 103.2518) 
    },
    {
        coords = vector3(-1391.1625, -614.9095, 29.7), offsetZ = -0.19, heading = 193.7300, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1391.2374, -614.2595, 28.7199, 10.1324)  
    },
    {
        coords = vector3(-1389.6996, -615.1091, 29.7), offsetZ = -0.19, heading = 147.2963, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1389.4395, -614.6944, 28.7167, 336.3031)  
    },
    { 
        coords = vector3(-1388.7562, -616.4821, 29.7), offsetZ = -0.19, heading = 102.1954, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1388.0944, -616.4515, 28.7198, 272.4791)  
    },
    { 
        coords = vector3(-1389.1012, -617.8816, 29.7), offsetZ = -0.19, heading = 59.3039, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1388.6301, -618.1818, 28.7168, 252.3978)  
    },
    { 
        coords = vector3(-1390.4135, -618.7637, 29.7), offsetZ = -0.19, heading = 17.3647, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1390.5574, -619.3263, 28.7199, 178.0379)  
    },
    { 
        coords = vector3(-1391.8851, -618.4701, 29.7), offsetZ = -0.19, heading = 334.0481, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1392.3083, -618.8624, 28.7197, 140.5256)  
    },
    { 
        coords = vector3(-1392.7683, -617.1564, 29.7), offsetZ = -0.19, heading = 281.7125, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1393.3785, -617.0562, 28.7163, 98.5303)  
    },
    {  
        coords = vector3(-1392.6349, -615.6823, 29.7), offsetZ = -0.19, heading = 240.9679, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1392.9387, -615.3951, 28.7198, 62.8065)  
    },
    { 
        coords = vector3(-1388.3204, -619.2962, 29.7), offsetZ = -0.19, heading = 201.1910, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1388.3519, -618.8022, 28.7195, 10.1063)  
    },
    { 
        coords = vector3(-1386.7404, -619.6068, 29.7), offsetZ = -0.19, heading = 146.1500, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1386.4681, -619.2087, 28.7157, 334.6783)  
    },
    { 
        coords = vector3(-1386.7404, -619.6068, 29.7), offsetZ = -0.19, heading = 98.1063, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1385.1620, -621.2437, 28.7140, 259.6798)  
    },
    { 
        coords = vector3(-1386.1628, -622.4720, 29.7), offsetZ = -0.19, heading = 62.8796, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1385.5656, -622.8224, 28.7189, 245.5845)   
    },
    { 
        coords = vector3(-1387.3622, -623.5470, 29.7), offsetZ = -0.19, heading = 14.7412, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1387.2804, -623.9882, 28.7168, 200.8639)   
    },
    { 
        coords = vector3(-1388.9063, -623.0493, 29.7), offsetZ = -0.19, heading = 333.1371, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1389.2611, -623.5258, 28.7136, 150.2943)   
    },
    { 
        coords = vector3(-1389.7645, -621.7282, 29.7), offsetZ = -0.19, heading = 279.7060, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1390.7634, -621.7236, 28.7195, 97.8556)   
    },
    { 
        coords = vector3(-1389.6356, -620.2726, 29.7), offsetZ = -0.19, heading = 241.3378, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1390.0424, -620.0990, 28.7140, 62.0677)   
    },
    { 
        coords = vector3(-1385.6876, -621.0190, 29.7), offsetZ = -0.19, heading = 98.5639, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1385.3055, -621.1281, 28.7197, 270.4620)   
    },
    -- MIDDLE SEDAČKY
    {
        coords = vector3(-1389.9762, -609.9224, 29.40), offsetZ = -0.19, heading = 121.2169, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1390.5178, -610.2568, 28.7132, 122.1431)
    },
    {
        coords = vector3(-1389.3031, -611.2119, 29.40), offsetZ = -0.19, heading = 126.8532, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1389.6567, -611.4376, 28.7124, 125.6381)
    },
    {
        coords = vector3(-1385.1166, -617.6378, 29.40), offsetZ = -0.19, heading = 125.1075, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1385.6500, -617.9906, 28.7180, 126.1672)
    },
    {
        coords = vector3(-1384.2188, -618.8585, 29.40), offsetZ = -0.19, heading = 114.5420, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1384.6682, -619.1216, 28.7164, 122.5537)
    },
    {
        coords = vector3(-1391.4355, -623.6467, 29.40), offsetZ = -0.19, heading = 303.9750, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1390.9292, -623.3126, 28.7190, 307.7499)
    },
    {
        coords = vector3(-1392.2627, -622.4487, 29.40), offsetZ = -0.19, heading = 298.2870, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1391.8646, -622.2059, 28.7188, 311.1368)
    },
    {
        coords = vector3(-1396.4188, -615.9283, 29.40), offsetZ = -0.19, heading = 320.4129, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1395.9799, -615.6464, 28.7142, 305.3438)
    },
    {
        coords = vector3(-1397.2181, -614.7031, 29.40), offsetZ = -0.19, heading = 300.3795, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1396.8365, -614.4402, 28.7152, 305.5517)
    },
    --NEXT TO BAR 2
    {
        coords = vector3(-1399.3093, -596.4437, 29.9), offsetZ = -0.19, heading = 306.2419, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1398.8873, -596.1819, 29.3192, 305.1827)
    }, 
    {
        coords = vector3(-1397.9879, -594.5098, 29.9), offsetZ = -0.19, heading = 212.7606, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1397.7053, -594.9571, 29.3336, 212.9481)
    },
    {
        coords = vector3(-1396.5959, -593.5567, 29.9), offsetZ = -0.19, heading = 211.9188, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1396.2491, -594.1116, 29.3212, 216.6523)
    },
    {
        coords = vector3(-1395.1464, -592.7272, 29.9), offsetZ = -0.19, heading = 218.1055, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1394.9690, -593.3770, 29.3192, 212.3411)
    },
    {
        coords = vector3(-1392.8221, -592.0961, 29.9), offsetZ = -0.19, heading = 124.1419, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1393.2581, -592.4245, 29.3156, 124.8236)
    },
    --VÝŘIVKA
    {
        coords = vector3(-1377.9521, -611.8181, 29.865), offsetZ = -0.19, heading = 212.4994, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1378.5038, -611.2585, 29.9199, 43.1978)
    },
    {
        coords = vector3(-1376.7573, -613.1389, 29.865), offsetZ = -0.19, heading = 43.4249, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1376.4963, -613.6484, 29.9199, 216.2159)
    },
    {
        coords = vector3(-1373.0588, -612.7699, 29.865), offsetZ = -0.19, heading = 130.4284, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1374.0946, -611.4827, 29.9207, 26.4724) 
    },
    {
        coords = vector3(-1374.5815, -613.9709, 29.865), offsetZ = -0.19, heading = 306.5699, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1375.1110, -614.3120, 29.9203, 125.0062) 
    },
    {
        coords = vector3(-1376.7451, -615.0785, 29.865), offsetZ = -0.19, heading = 116.3235, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1376.0594, -615.0432, 29.9209, 307.3955) 
    },
    {
        coords = vector3(-1378.2430, -616.2068, 29.865), offsetZ = -0.19, heading = 296.6765, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1379.3517, -615.3387, 29.9204, 31.5904) 
    },
    {
        coords = vector3(-1382.0740, -606.7398, 29.865), offsetZ = -0.19, heading = 303.4243, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1383.2388, -607.5933, 29.9201, 119.4447) 
    },
    {
        coords = vector3(-1380.6664, -605.7576, 29.865), offsetZ = -0.19, heading = 122.7159, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1379.3958, -604.7578, 29.9199, 208.0502) 
    },
    {
        coords = vector3(-1380.0627, -602.0237, 29.865), offsetZ = -0.19, heading = 115.9990, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1379.0793, -602.7892, 29.9199, 213.1682) 
    },
    {
        coords = vector3(-1381.5472, -603.1871, 29.865), offsetZ = -0.19, heading = 302.9253, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1382.2565, -603.5339, 29.9199, 204.6322) 
    },
    {
        coords = vector3(-1383.5153, -604.6767, 29.865), offsetZ = -0.19, heading = 115.2440, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1382.9504, -604.0244, 29.9199, 207.2239) 
    },
    {
        coords = vector3(-1385.0482, -605.6738, 29.865), offsetZ = -0.19, heading = 311.4914, radius = 0.5, distance = 1.7,    
        LeaveCoords = vector4(-1384.8961, -606.8112, 29.9202, 213.4641) 
    },
    --PŘÍCHOZÍ SÁL
    {
        coords = vector3(-1383.7639, -589.1394, 29.99), offsetZ = -0.19, heading = 131.0272, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1384.1426, -589.4099, 29.3134, 126.6820)
    },
    {
        coords = vector3(-1384.4240, -588.0487, 29.99), offsetZ = -0.19, heading = 218.0341, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1384.8335, -588.3032, 29.3185, 127.9101)
    },
    {
        coords = vector3(-1390.4857, -589.7709, 29.99), offsetZ = -0.19, heading = 114.2703, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1390.2695, -590.0991, 29.3086, 212.6344)
    },
    {
        coords = vector3(-1391.3545, -590.2064, 29.99), offsetZ = -0.19, heading = 211.8209, radius = 0.5, distance = 1.7,          
        LeaveCoords = vector4(-1391.0862, -590.6265, 29.3169, 215.4964)
    },


} 